# Drag and Drop Functionality for Chat Management

## Overview

This implementation adds comprehensive drag-and-drop functionality to the Swiss Knowledge Hub chat interface, allowing users to organize their chats by moving them between groups and reordering them within groups.

## Features Implemented

### 1. Drag Chats into Groups
- Users can drag individual chats from the "Chat-Verlauf" (Chat History) section
- Drop them into any specific group (e.g., Gruppe 1, Gruppe 2)
- Visual feedback shows when hovering over a valid drop target

### 2. Move Chats Between Groups
- Chats already assigned to a group can be moved to different groups
- Drag from one group and drop into another
- Real-time visual indicators show valid drop zones

### 3. Move Chats Within Groups (Reordering)
- Chats within the same group can be reordered via drag-and-drop
- Maintains the order preference for better organization
- Smooth animations during reordering

### 4. Remove Chats from Groups
- Drag chats from any group back to the "Chat History" section
- This removes them from the group (sets groupId to null)
- Visual feedback indicates when dropping will remove from group

## Technical Implementation

### Libraries Used
- `@dnd-kit/core` - Core drag-and-drop functionality
- `@dnd-kit/sortable` - Sortable list functionality
- `@dnd-kit/utilities` - Utility functions for transforms

### Key Components

#### 1. `DraggableChatItem`
- Makes individual chat items draggable
- Shows drag handle on hover
- Provides visual feedback during drag operations

#### 2. `DroppableGroup`
- Makes group containers accept dropped chats
- Handles sorting within groups
- Shows drop indicators when hovering

#### 3. `DroppableChatHistory`
- Makes the chat history section a drop target
- Allows removing chats from groups
- Visual feedback for ungrouping operations

#### 4. Updated `MemberAppSidebar`
- Wraps everything in `DndContext`
- Handles drag events and API calls
- Manages state updates and error handling

### API Integration
- Uses existing `updateChat` service to modify chat group assignments
- Updates the `groupId` field when moving chats
- Provides user feedback via toast notifications
- Handles errors gracefully

## Visual Features

### Drag Indicators
- Grip handle appears on hover for each chat item
- Drag overlay shows what's being dragged
- Drop zones highlight when valid targets are hovered

### Animations
- Smooth transitions during drag operations
- Pulse animations for drop indicators
- Opacity changes for dragged items

### Accessibility
- Touch-friendly drag handles
- Proper ARIA labels for screen readers
- Keyboard navigation support (via @dnd-kit)

## Usage Instructions

1. **To move a chat to a group:**
   - Hover over a chat in the Chat History section
   - Click and drag the grip handle that appears
   - Drop it onto the desired group

2. **To move between groups:**
   - Hover over a chat in any group
   - Drag it to a different group
   - Drop to move

3. **To reorder within a group:**
   - Drag a chat within the same group
   - Drop it at the desired position

4. **To remove from a group:**
   - Drag a chat from any group
   - Drop it in the Chat History section

## Error Handling

- Network errors are caught and displayed via toast notifications
- Failed operations don't change the UI state
- Loading states are shown during API calls
- Graceful fallbacks for edge cases

## Performance Considerations

- Optimistic updates for better UX
- Minimal re-renders during drag operations
- Efficient collision detection
- Touch-friendly for mobile devices

## Recent Fix: Cross-Context Dragging

### Problem
Initially, users could drag within groups but not drag items from groups to outside areas (like Chat History). This was caused by having separate `SortableContext` components for each group, which constrained drag operations within their boundaries.

### Solution
- **Moved to Global SortableContext**: All draggable items are now within a single `SortableContext` at the top level
- **Improved Collision Detection**: Changed from `closestCenter` to `closestCorners` for better drop target detection
- **Enhanced Debugging**: Added console logging to track drag operations and identify issues

### Technical Changes
1. **Removed individual SortableContext** from `DroppableGroup` and `DroppableChatHistory`
2. **Added global SortableContext** in `MemberAppSidebar` that includes all chat IDs
3. **Improved collision detection** algorithm for better cross-context dragging
4. **Added debugging logs** to track drag events and target detection

### Testing the Fix
1. **Open browser console** to see drag operation logs
2. **Try dragging from group to Chat History** - should now work
3. **Try dragging between different groups** - should work smoothly
4. **Try reordering within groups** - should continue working
5. **Check console logs** for any errors or unexpected behavior

### Debug Information
When testing, you'll see console logs like:
- `Drag started: chat-123 {type: "chat", chat: {...}, groupId: "group1"}`
- `Drag over: chat-history {type: "chat-history"}`
- `Drag ended: {activeId: "chat-123", overId: "chat-history", ...}`
- `Dropping into chat history (ungrouping)`
- `Group change: group1 -> null`

This helps identify if drag operations are being detected correctly.
