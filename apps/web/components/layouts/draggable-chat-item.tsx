"use client";

import * as React from "react";
import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { GripVertical, History } from "lucide-react";
import Link from "next/link";
import { GroupMenuItem } from "./nav-projects";
import { cn } from "@/lib/utils";

interface DraggableChatItemProps {
  chat: any;
  chatIndex: number;
  groupId?: string;
  isActive: boolean;
  editingIndex: number | null;
  editingName: string;
  setEditingName: (name: string) => void;
  handleNameSubmit: (chat: any) => void;
  setEditingIndex: (index: number | null) => void;
  openDropdown: number | null;
  setOpenDropdown: (index: number | null) => void;
  hoveredProject: number | null;
  setHoveredProject: (index: number | null) => void;
  groups: any[];
  handleStartEditing: (index: number, name: string) => void;
  router: any;
  isDragging?: boolean;
}

export function DraggableChatItem({
  chat,
  chatIndex,
  groupId,
  isActive,
  editingIndex,
  editingName,
  setEditingName,
  handleNameSubmit,
  setEditingIndex,
  openDropdown,
  setOpenDropdown,
  hoveredProject,
  setHoveredProject,
  groups,
  handleStartEditing,
  router,
  isDragging = false,
}: DraggableChatItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({
    id: `chat-${chat.id}`,
    data: {
      type: "chat",
      chat,
      groupId,
    },
  });

  // Debug logging (can be removed later)
  console.log("🔍 DraggableChatItem rendered:", {
    chatId: chat.id,
    sortableId: `chat-${chat.id}`,
    groupId,
    hasListeners: !!listeners,
    hasAttributes: !!attributes,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  };

  const chatUrl = groupId ? `/ask-ai/${groupId}` : `/ask-ai/${chat.id}/`;

  return (
    <li
      ref={setNodeRef}
      style={style}
      {...attributes}
      {...listeners}
      onMouseEnter={() => setHoveredProject(chatIndex)}
      onMouseLeave={() => setHoveredProject(null)}
      className={cn(
        "flex items-center gap-1 group/chat-item cursor-grab active:cursor-grabbing",
        isDragging && "opacity-50"
      )}
      onMouseDown={() => {
        console.log("🔥 ENTIRE ITEM clicked for chat:", chat.id);
      }}
    >
      {/* Drag Handle - Visual indicator only */}
      <div className="flex-shrink-0 p-1 opacity-0 group-hover/chat-item:opacity-100 transition-opacity">
        <GripVertical className="h-3 w-3 text-sidebar-foreground/50" />
      </div>

      {/* Chat Link */}
      <Link
        href={chat.url || chatUrl}
        className={cn(
          "flex items-center gap-2 rounded-md py-1.5 px-2 text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground w-full",
          isActive
            ? "bg-sidebar-accent text-sidebar-accent-foreground"
            : ""
        )}
        onClick={(e) => {
          // Allow navigation, but prevent if dragging
          if (isSortableDragging) {
            e.preventDefault();
            e.stopPropagation();
          }
        }}
      >
        <History className="size-3.5 shrink-0" />
        <div className="w-full overflow-hidden">
          {editingIndex === chatIndex ? (
            <input
              type="text"
              value={editingName}
              onChange={(e) => setEditingName(e.target.value)}
              onBlur={() => handleNameSubmit(chat)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleNameSubmit(chat);
                } else if (e.key === "Escape") {
                  setEditingIndex(null);
                }
              }}
              className="bg-transparent border-none outline-none w-full max-w-[185px] text-sm focus:ring-2 focus:ring-primary focus-visible:ring-2 focus-visible:ring-primary"
              autoFocus={true}
            />
          ) : (
            <span
              className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
              title={chat?.title}
            >
              {chat?.title}
            </span>
          )}
        </div>
      </Link>

      {/* Menu Options */}
      <GroupMenuItem
        router={router}
        index={chatIndex}
        item={{
          ...chat,
          name: chat?.title,
        }}
        openDropdown={openDropdown}
        setOpenDropdown={setOpenDropdown}
        hoveredProject={hoveredProject}
        showMore={true}
        groups={groups}
        handleStartEditing={handleStartEditing}
      />
    </li>
  );
}
