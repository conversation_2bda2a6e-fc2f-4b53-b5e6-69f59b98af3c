# Drag and Drop Functionality for Chat Management

## Overview

This implementation adds comprehensive drag-and-drop functionality to the Swiss Knowledge Hub chat interface, allowing users to organize their chats by moving them between groups and reordering them within groups.

## Features Implemented

### 1. Drag Chats into Groups
- Users can drag individual chats from the "Chat-Verlauf" (Chat History) section
- Drop them into any specific group (e.g., Gruppe 1, Gruppe 2)
- Visual feedback shows when hovering over a valid drop target

### 2. Move Chats Between Groups
- Chats already assigned to a group can be moved to different groups
- Drag from one group and drop into another
- Real-time visual indicators show valid drop zones

### 3. Move Chats Within Groups (Reordering)
- Chats within the same group can be reordered via drag-and-drop
- Maintains the order preference for better organization
- Smooth animations during reordering
- **NEW**: Optimistic updates with API persistence

### 5. Reorder Chats in Chat History
- **NEW**: Ungrouped chats in the Chat History section can be reordered
- Drag and drop to change the order of ungrouped chats
- Order is persisted via API calls

### 4. Remove Chats from Groups
- Drag chats from any group back to the "Chat History" section
- This removes them from the group (sets groupId to null)
- Visual feedback indicates when dropping will remove from group

## Technical Implementation

### Libraries Used
- `@dnd-kit/core` - Core drag-and-drop functionality
- `@dnd-kit/sortable` - Sortable list functionality
- `@dnd-kit/utilities` - Utility functions for transforms

### Key Components

#### 1. `DraggableChatItem`
- Makes individual chat items draggable using `useSortable`
- Shows drag handle on hover with grip icon
- Provides visual feedback during drag operations (opacity changes)
- Supports both group and chat history contexts

#### 2. `DroppableGroup`
- Makes group containers accept dropped chats using `useDroppable`
- **NEW**: Wraps chat items in `SortableContext` for reordering within groups
- Shows drop indicators when hovering (empty state and overlay)
- Handles both adding new chats and reordering existing ones

#### 3. `DroppableChatHistory`
- Makes the chat history section a drop target using `useDroppable`
- **NEW**: Wraps chat items in `SortableContext` for reordering within chat history
- Visual feedback for ungrouping operations and empty states
- Shows appropriate messages based on context

#### 4. Enhanced `MemberAppSidebar`
- Wraps everything in `DndContext` with improved collision detection
- **NEW**: Handles complex drag events with optimistic state management
- **NEW**: Manages API calls for moving, ungrouping, and reordering
- **NEW**: Implements error handling with state rollback

### API Integration
- Uses existing `updateChat` service to modify chat group assignments
- **NEW**: Uses new `updateChatOrder` service for reordering operations
- Updates the `groupId` field when moving chats
- **NEW**: Updates chat order for reordering within groups and chat history
- Provides user feedback via toast notifications with internationalization
- **NEW**: Implements optimistic updates with rollback on errors
- Handles errors gracefully with state restoration

## Visual Features

### Drag Indicators
- Grip handle appears on hover for each chat item
- Drag overlay shows what's being dragged
- Drop zones highlight when valid targets are hovered

### Animations
- Smooth transitions during drag operations
- Pulse animations for drop indicators
- Opacity changes for dragged items

### Accessibility
- Touch-friendly drag handles
- Proper ARIA labels for screen readers
- Keyboard navigation support (via @dnd-kit)

## Usage Instructions

1. **To move a chat to a group:**
   - Hover over a chat in the Chat History section
   - Click and drag the grip handle that appears
   - Drop it onto the desired group

2. **To move between groups:**
   - Hover over a chat in any group
   - Drag it to a different group
   - Drop to move

3. **To reorder within a group:**
   - Drag a chat within the same group
   - Drop it at the desired position
   - **NEW**: Order is saved automatically

4. **To reorder in chat history:**
   - **NEW**: Drag a chat within the Chat History section
   - Drop it at the desired position
   - Order is saved automatically

5. **To remove from a group:**
   - Drag a chat from any group
   - Drop it in the Chat History section

## Error Handling

- Network errors are caught and displayed via toast notifications
- Failed operations don't change the UI state
- Loading states are shown during API calls
- Graceful fallbacks for edge cases

## Performance Considerations

- **NEW**: Optimistic updates for better UX during reordering
- Minimal re-renders during drag operations
- Efficient collision detection with `closestCorners`
- Touch-friendly for mobile devices
- **NEW**: State management with automatic rollback on errors

## Backend API Enhancements

### New API Endpoint: `/api/chat/[chatId]/order`
- **Method**: PUT
- **Purpose**: Update chat order within groups or chat history
- **Parameters**:
  - `order`: Number - The new order position
  - `groupId`: String | null - The group ID (null for chat history)
- **Response**: Updated chat object
- **Error Handling**: Proper error responses with rollback support

### Service Functions
- **`updateChatOrder(data)`**: New service function for reordering
- **`updateChat(data)`**: Existing service for group assignments
- Both functions include error handling and response validation

## Recent Fix: Cross-Context Dragging

### Problem
Initially, users could drag within groups but not drag items from groups to outside areas (like Chat History). This was caused by having separate `SortableContext` components for each group, which constrained drag operations within their boundaries.

### Solution
- **Moved to Global SortableContext**: All draggable items are now within a single `SortableContext` at the top level
- **Improved Collision Detection**: Changed from `closestCenter` to `closestCorners` for better drop target detection
- **Enhanced Debugging**: Added console logging to track drag operations and identify issues

### Technical Changes
1. **Removed individual SortableContext** from `DroppableGroup` and `DroppableChatHistory`
2. **Added global SortableContext** in `MemberAppSidebar` that includes all chat IDs
3. **Improved collision detection** algorithm for better cross-context dragging
4. **Added debugging logs** to track drag events and target detection

### Testing the Fix
1. **Open browser console** to see drag operation logs
2. **Try dragging from group to Chat History** - should now work
3. **Try dragging between different groups** - should work smoothly
4. **Try reordering within groups** - should continue working
5. **Check console logs** for any errors or unexpected behavior

### Debug Information
When testing, you'll see console logs like:
- `Drag started: chat-123 {type: "chat", chat: {...}, groupId: "group1"}`
- `Drag over: chat-history {type: "chat-history"}`
- `Drag ended: {activeId: "chat-123", overId: "chat-history", ...}`
- `Dropping into chat history (ungrouping)`
- `Group change: group1 -> null`

This helps identify if drag operations are being detected correctly.
