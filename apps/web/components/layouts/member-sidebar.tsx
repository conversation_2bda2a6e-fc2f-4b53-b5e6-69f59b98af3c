"use client";

import * as React from "react";
import {
  Folder,
  Plus,
  FileText,
  Users,
} from "lucide-react";
import {
  DndContext,
  DragEndEvent,
  DragOverEvent,
  DragStartEvent,
  pointerWithin
} from "@dnd-kit/core";
import { SortableContext } from "@dnd-kit/sortable";

// Import removed since we're implementing custom scrolling
import { NavProjects } from "@/components/layouts/nav-projects";
import { NavSecondary } from "@/components/layouts/nav-secondary";
import { NavUser } from "@/components/layouts/nav-user";
import {
  Sidebar,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
} from "@/components/layouts/sidebar";
import { OrganizationSwitcher } from "@/components/organizations/organization-switcher";
import { Button } from "../ui/button";
import { CreateGroupDialog } from "../model/create-group-dialog";
import { usePathname, useRouter } from "next/navigation";
import { updateChat } from "@/services";
import toast from "react-hot-toast";
import { useLanguage } from "@/lib/language-context";
import { DroppableGroup } from "./droppable-group";
import { DroppableChatHistory } from "./droppable-chat-history";

// Default workspaces - adding more to test scrolling

const data = {
  user: {
    name: "SKH ",
    role: "Admin",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [],
  navSecondary: [
    {
      title: "Help",
      url: `${process.env.NEXT_PUBLIC_API_BASE_URL}/docs/en`,
      icon: FileText,
      translationKey: "sidebar.help",
    },
  ],
  projects: [
    {
      name: "Shared Threads",
      url: "/shared-threads",
      icon: Users,
      translationKey: "sidebar.sharedThreads",
    },
  ],
};

export function MemberAppSidebar({
  session,
  group,
  chatHistory,
  tenantId,
  ...props
}: any) {
  const [openDropdown, setOpenDropdown] = React.useState<number | null>(null);

  const [editingIndex, setEditingIndex] = React.useState<number | null>(null);
  const [editingName, setEditingName] = React.useState("");

  const handleStartEditing = (index: number, name: string) => {
    setEditingIndex(index);
    setEditingName(name);
    setOpenDropdown(null);
  };

  const handleNameSubmit = async (chat: any) => {
    if (editingName.trim() === "") return;

    try {
      const response = await updateChat({
        id: chat.id,
        title: editingName,
      });
      if (response.error) {
        toast.error(response.error);
      } else {
        toast.success(t("chat.renameSuccess"));
        window.location.reload();
      }
    } catch (error) {
      toast.error(t("chat.renameFailed"));
    } finally {
      setEditingIndex(null);
    }
  };

  const pathname = usePathname() || "";
  const router = useRouter();
  const { t } = useLanguage();
  const [hoveredProject, setHoveredProject] = React.useState<number | null>(
    null
  );
  const user = { ...session?.user, role: session?.memberships?.[0]?.role };
  const [groups] = React.useState(() => {
    // Fall back to group prop if no temporary data
    return (
      group?.map((g: any) => ({
        id: g.id,
        title: g.name,
        icon: Folder,
        isActive: false,
        items: g?.chats?.map((chat: any) => ({
          id: chat.id,
          title: chat.title,
          url: `/ask-ai/${chat.id}/`,
        })),
      })) || []
    );
  });

  // State to track expanded groups
  const [expandedGroups, setExpandedGroups] = React.useState<
    Record<string, boolean>
  >({});

  // State to track group chats (using only the setter)
  const [, setGroupChats] = React.useState<Record<string, any[]>>({});

  // Add state for drag and drop
  const [draggedOverId, setDraggedOverId] = React.useState<string | null>(null);

  // State for optimistic updates during reordering
  const [optimisticChatHistory, setOptimisticChatHistory] = React.useState<any[]>([]);
  const [optimisticGroups, setOptimisticGroups] = React.useState<any[]>([]);
  
  // Create a flat array of all chat IDs for SortableContext
  const allChatIds = React.useMemo(() => {
    const ids: string[] = [];
    
    // Add group chat IDs
    groups.forEach((g: any) => {
      if (g.items) {
        g.items.forEach((chat: any) => {
          ids.push(`chat-${chat.id}`);
        });
      }
    });
    
    // Add chat history IDs
    if (chatHistory) {
      chatHistory.forEach((chat: any) => {
        ids.push(`chat-${chat.id}`);
      });
    }
    
    return ids;
  }, [groups, chatHistory]);

  // Initialize optimistic state from props
  React.useEffect(() => {
    setOptimisticChatHistory(chatHistory || []);
    setOptimisticGroups(groups || []);
  }, [chatHistory, groups]);

  // Use simple pointer-based collision detection for better reliability
  const customCollisionDetection = pointerWithin;

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    console.log("Drag started:", active.id, active.data.current);
  };

  // Handle drag over
  const handleDragOver = (event: DragOverEvent) => {
    const { active, over } = event;
    setDraggedOverId(over?.id as string || null);

    console.log("🔍 Drag over:", {
      overId: over?.id,
      overData: over?.data.current,
      activeId: active.id,
      activeData: active.data.current
    });
  };

  // Handle drag end
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    console.log("dragevent",event)
    setDraggedOverId(null);

    if (!over) {
      // Reset optimistic state if drag was cancelled
      setOptimisticChatHistory(chatHistory || []);
      setOptimisticGroups(groups || []);
      return;
    }

    console.log("🎯 Drag ended:", {
      activeId: active.id,
      overId: over.id,
      activeData: active.data.current,
      overData: over.data.current
    });

    // Extract data
    const activeData = active.data.current as any;
    const overData = over.data.current as any;

    console.log("ActiveDataOverData",activeData,overData)

    if (!activeData) {
      console.log("❌ No active data");
      return;
    }

    // Only handle chat items being dragged
    if (activeData.type !== "chat") {
      console.log("❌ Not a chat item");
      return;
    }

    const chatId = activeData.chat.id;
    const currentGroupId = activeData.groupId;

    // SCENARIO 1: Dropping into a group
    if (overData && overData.type === "group") {
      const targetGroupId = overData.groupId;

      console.log("📁 Dropping into group:", {
        chatId,
        fromGroup: currentGroupId || "chat-history",
        toGroup: targetGroupId
      });

      // Skip if same group
      if (targetGroupId === currentGroupId) {
        console.log("⚠️ Same group, skipping");
        return;
      }

      try {
        toast.loading(t("chat.moving"));
        const response = await updateChat({
          id: chatId,
          groupId: targetGroupId,
        });

        toast.remove();
        if (response.error) {
          toast.error(response.error);
        } else {
          toast.success(t("chat.moveSuccess"));
          // window.location.reload();
        }
      } catch (error) {
        toast.error(t("chat.moveFailed"));
      }
      return;
    }
    
    // SCENARIO 2: Dropping into chat history (ungrouping)
    if (overData && overData.type === "chat-history") {
      console.log("🔄 Dropping into chat history (ungrouping)");

      // Skip if already ungrouped
      if (!currentGroupId) {
        console.log("⚠️ Chat is already ungrouped, skipping");
        return;
      }

      console.log("📤 Moving chat from group to chat history:", {
        chatId,
        fromGroupId: currentGroupId
      });

      try {
        toast.loading(t("chat.moving"));
        const response = await updateChat({
          id: chatId,
          groupId: null, // Set to null to ungroup
        });

        console.log("Ungroup response:", response);
        toast.remove();
        if (response.error) {
          toast.error(response.error);
        } else {
          toast.success(t("chat.moveSuccess"));
          // window.location.reload();
        }
      } catch (error) {
        toast.error(t("chat.moveFailed"));
      }
      return;
    }

    // SCENARIO 3: Dropping onto another chat (could be moving to different group or reordering)
    if (overData && overData.type === "chat") {
      const targetGroupId = overData.groupId;

      console.log("🔄 Chat-to-chat operation:", {
        chatId,
        currentGroup: currentGroupId || "chat-history",
        targetGroup: targetGroupId || "chat-history"
      });

      // If different containers, this is a move operation
      if (currentGroupId !== targetGroupId) {
        console.log("📁 Moving chat to different group via chat drop");

        try {
          toast.loading(t("chat.moving"));
          const response = await updateChat({
            id: chatId,
            groupId: targetGroupId, // targetGroupId could be null for chat history
          });

          toast.remove();
          if (response.error) {
            toast.error(response.error);
          } else {
            toast.success(t("chat.moveSuccess"));
            // window.location.reload();
          }
        } catch (error) {
          toast.error(t("chat.moveFailed"));
        }
        return;
      }

      // Same container - this would be reordering
      console.log("✅ Same container reordering - not implemented yet");
      return;
    }

    // SCENARIO 4: No valid drop target or unhandled case
    console.log("❓ Unhandled drag operation:", {
      activeType: activeData?.type,
      overType: overData?.type,
      activeGroupId: currentGroupId,
      overGroupId: overData?.groupId,
      overId: over.id
    });
  };

  // Toggle group expansion
  const toggleGroupExpansion = (groupName: string) => {
    setExpandedGroups((prev) => ({
      ...prev,
      [groupName]: !prev[groupName],
    }));
  };

  React.useEffect(() => {
    const group =
      groups?.find(
        (g: any) => g?.items?.find((c: any) => c?.url === pathname)
      ) || [];
    setExpandedGroups((prev) => ({
      ...prev,
      [group?.title]: true,
    }));
  }, [groups]);

  // Check if a group or its chat is active
  const isGroupActive = (groupId: string) => {
    return pathname.includes(`/ask-ai/${groupId}`);
  };

  // Check if a specific chat is active
  const isChatActive = (chatId: string) => {
    return pathname === `/ask-ai/${chatId}/`;
  };

  // Initialize chats from the group prop
  React.useEffect(() => {
    if (groups) {
      const newGroupChats: Record<string, any[]> = {};

      groups.forEach((g: any) => {
        if (g.id && g.chats) {
          newGroupChats[g.id] = g.chats.map((chat: any) => ({
            id: chat.id,
            name: chat.name,
            icon: Folder,
            type: "chat",
            createdAt: chat.createdAt,
          }));

          // Auto-expand group if it's active
          if (isGroupActive(g.id)) {
            setExpandedGroups((prev) => ({
              ...prev,
              [g.id]: true,
            }));
          }
        }
      });

      setGroupChats(newGroupChats);
    }
  }, [groups, pathname]); // Added pathname as dependency

  return (
    <Sidebar
      className="top-[--header-height] !h-[calc(100svh-var(--header-height))]"
      {...props}
    >
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="px-2">
              <OrganizationSwitcher isAdmin={false} />
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <div className="flex min-h-0 flex-1 flex-col gap-2 p-2 overflow-hidden">
        <a href="/ask-ai" className="shrink-0 mb-2">
          <Button
            variant="secondary"
            size="sm"
            className="w-full justify-between rounded-full px-4"
          >
            {t("memberSidebar.newChat")}
            <Plus className="h-4 w-4 " />
          </Button>
        </a>{" "}
        <div className="shrink-0">
          <NavProjects projects={data.projects} />
        </div>
        <div className="flex items-center justify-between px-2 py-1 shrink-0">
          <div className="text-xs font-medium text-sidebar-foreground/70">
            {t("memberSidebar.chatHistory")}
          </div>

          <CreateGroupDialog
            trigger={
              <Button size="icon" variant="ghost">
                <Plus className="h-4 w-4" />
                <span className="sr-only">
                  {t("memberSidebar.createGroup")}
                </span>
              </Button>
            }
            tenantId={tenantId}
          />
        </div>
        <DndContext
          onDragStart={handleDragStart}
          onDragOver={handleDragOver}
          onDragEnd={handleDragEnd}
          collisionDetection={customCollisionDetection}
        >
          <SortableContext items={allChatIds}>
            <div className="overflow-y-auto flex w-full h-full min-w-0 flex-col">
              <ul className="flex w-full min-w-0 flex-col gap-1">
                {group.map((item: any) => {
                  const groupName = item.name;
                  const isActive = isGroupActive(item.id);
                  const isExpanded = expandedGroups[groupName];
                  // Use optimistic state for chats
                  const chats =
                    optimisticGroups.find((g: { id: string }) => g.id === item.id)?.items ||
                    groups.find((g: { id: string }) => g.id === item.id)?.items ||
                    [];
                  const isOver = draggedOverId === `group-${item.id}`;

                  return (
                    <DroppableGroup
                      key={item.id}
                      group={item}
                      chats={chats}
                      isActive={isActive}
                      isExpanded={isExpanded}
                      toggleGroupExpansion={toggleGroupExpansion}
                      editingIndex={editingIndex}
                      editingName={editingName}
                      setEditingName={setEditingName}
                      handleNameSubmit={handleNameSubmit}
                      setEditingIndex={setEditingIndex}
                      openDropdown={openDropdown}
                      setOpenDropdown={setOpenDropdown}
                      hoveredProject={hoveredProject}
                      setHoveredProject={setHoveredProject}
                      groups={optimisticGroups.length > 0 ? optimisticGroups : groups}
                      handleStartEditing={handleStartEditing}
                      router={router}
                      isChatActive={isChatActive}
                      isOver={isOver}
                    />
                  );
                })}
              </ul>

              <DroppableChatHistory
                chatHistory={(optimisticChatHistory.length > 0 ? optimisticChatHistory : chatHistory)?.map((item: any) => ({
                  id: item?.id,
                  name: item?.name,
                  url: item?.url,
                }))}
                editingIndex={editingIndex}
                editingName={editingName}
                setEditingName={setEditingName}
                handleNameSubmit={handleNameSubmit}
                setEditingIndex={setEditingIndex}
                openDropdown={openDropdown}
                setOpenDropdown={setOpenDropdown}
                hoveredProject={hoveredProject}
                setHoveredProject={setHoveredProject}
                groups={optimisticGroups.length > 0 ? optimisticGroups : groups}
                handleStartEditing={handleStartEditing}
                router={router}
                isChatActive={isChatActive}
                isOver={draggedOverId === "chat-history"}
              />
            </div>
          </SortableContext>
        </DndContext>
        <div className="mx-2 h-px bg-sidebar-border shrink-0" />
        <div className="shrink-0 mt-auto">
          <NavSecondary items={data.navSecondary} />
        </div>
      </div>
      <SidebarFooter>
        <NavUser
          user={{
            name: user?.name ?? user?.email,
            role: user?.role,
            avatar: user?.image,
          }}
        />
      </SidebarFooter>
    </Sidebar>
  );
}
