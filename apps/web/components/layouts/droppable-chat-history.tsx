"use client";

import * as React from "react";
import { useDroppable } from "@dnd-kit/core";
import { History } from "lucide-react";
import { DraggableChatItem } from "./draggable-chat-item";
import { cn } from "@/lib/utils";

interface DroppableChatHistoryProps {
  chatHistory: any[];
  editingIndex: number | null;
  editingName: string;
  setEditingName: (name: string) => void;
  handleNameSubmit: (chat: any) => void;
  setEditingIndex: (index: number | null) => void;
  openDropdown: number | null;
  setOpenDropdown: (index: number | null) => void;
  hoveredProject: number | null;
  setHoveredProject: (index: number | null) => void;
  groups: any[];
  handleStartEditing: (index: number, name: string) => void;
  router: any;
  isChatActive: (chatId: string) => boolean;
  isOver?: boolean;
}

export function DroppableChatHistory({
  chatHistory,
  editingIndex,
  editingName,
  setEditingName,
  handleNameSubmit,
  setEditingIndex,
  openDropdown,
  setOpenDropdown,
  hoveredProject,
  setHoveredProject,
  groups,
  handleStartEditing,
  router,
  isChatActive,
  isOver = false,
}: DroppableChatHistoryProps) {
  const { setNodeRef } = useDroppable({
    id: "chat-history",
    data: {
      type: "chat-history",
    },
  });

  React.useEffect(() => {
    console.log("chatHistoryuseEffect",chatHistory)
  },[])

  return (
    <div className="h-full">
      <div className="relative flex w-full min-w-0 flex-col">
        <div className="text-xs font-medium text-sidebar-foreground/70 mb-2 px-2">
          Chat History (Drop here to ungroup)
        </div>
        <div
          ref={setNodeRef}
          className={cn(
            "flex w-full min-w-0 flex-col gap-1 min-h-[150px] p-4 rounded-md transition-colors border-2 border-dashed border-transparent",
            isOver && "bg-primary/10 ring-2 ring-primary/50 border-primary/50"
          )}
        >
          {chatHistory?.map((item: any, index: number) => {
            const chatIsActive = isChatActive(item?.id);
            return (
              <DraggableChatItem
                key={item.id}
                chat={{
                  id: item?.id,
                  title: item?.name,
                  url: item?.url,
                }}
                chatIndex={index}
                isActive={chatIsActive}
                editingIndex={editingIndex}
                editingName={editingName}
                setEditingName={setEditingName}
                handleNameSubmit={handleNameSubmit}
                setEditingIndex={setEditingIndex}
                openDropdown={openDropdown}
                setOpenDropdown={setOpenDropdown}
                hoveredProject={hoveredProject}
                setHoveredProject={setHoveredProject}
                groups={groups}
                handleStartEditing={handleStartEditing}
                router={router}
              />
            );
          })}

          {/* Drop indicator when over */}
          {isOver && (
            <div className="absolute inset-0 border-2 border-dashed border-primary/50 rounded-md bg-primary/5 flex items-center justify-center pointer-events-none">
              <div className="text-center">
                <History className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-sm text-primary font-medium">
                  {chatHistory?.length === 0 ? "Drop chat here" : "Drop to remove from group"}
                </p>
              </div>
            </div>
          )}

          {/* Empty state */}
          {(!chatHistory || chatHistory.length === 0) && !isOver && (
            <div className="flex items-center justify-center h-full text-sidebar-foreground/50">
              <div className="text-center">
                <History className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No ungrouped chats</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
