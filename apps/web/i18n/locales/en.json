{"common": {"create": "Create", "selectAll": "Select All", "deselectAll": "Deselect All", "assign": "Assign", "users": "Users", "add": "Add", "adding": "Adding", "assigning": "Assigning...", "welcome": "Welcome to Swiss Knowledge Hub", "signIn": "Sign In", "signInNow": "Sign In Now", "signOut": "Sign Out", "register": "Register", "forgotPassword": "Forgot Password", "profile": "Profile", "settings": "Settings", "dashboard": "Dashboard", "organizations": "Organizations", "createOrganization": "Create Organization", "organizationSettings": "Organization Settings", "members": "Members", "invitations": "Invitations", "workspaces": "Workspaces", "workspace": "Workspace", "contentImportedAsHtml": "The content will be imported as HTML.", "email": "Email", "password": "Password", "name": "Name", "description": "Description", "save": "Save", "cancel": "Cancel", "yes": "Yes", "loading": "Loading...", "submit": "Submit", "invite": "Invite", "remove": "Remove", "creating": "Creating...", "move": "Move", "moving": "Moving...", "rename": "<PERSON><PERSON>", "edit": "Edit", "search": "Search", "language": "Language", "english": "English", "german": "German", "firstName": "First Name", "lastName": "Last Name", "saving": "Saving...", "saveChanges": "Save Changes", "tryAgain": "Try again", "returnToSignIn": "Return to sign in", "emailSent": "Email sent", "checkEmail": "Check your email", "tryAnotherEmail": "Try another email", "notifications": "Notifications", "yourOrganizations": "Your Organizations", "current": "Current", "optional": "optional", "back": "Back", "you": "You", "actions": "Actions", "user": "User", "error": "Error", "success": "Success", "delete": "Delete", "update": "Update", "filter": "Filter", "sort": "Sort", "next": "Next", "previous": "Previous", "select": "Select", "noPermission": "You don't have permission to perform this action", "accessDenied": "Access Denied", "noPermissionToView": "You don't have permission to view this content", "fetch": "<PERSON>tch", "preview": "Preview", "title": "Title", "content": "Content", "warning": "Warning", "urlRequired": "URL is required", "titleAndContentRequired": "Title and content are required"}, "auth": {"loginTitle": "Sign in to your account", "registerTitle": "Create a new account", "emailPlaceholder": "Enter your email", "passwordPlaceholder": "Enter your password", "confirmPassword": "Confirm Password", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "signUp": "Sign up", "resetPassword": "Reset Password", "resetPasswordDescription": "Enter your email to receive a password reset link", "resetEmailSent": "Check your email for reset instructions", "resetEmailMessage": "We've sent an email to {email} with instructions to reset your password. Please check your inbox.", "sendResetInstructions": "Send Reset Instructions", "accountInformation": "Account Information", "agreeToTerms": "You must agree to the terms and conditions.", "passwordsDoNotMatch": "Passwords do not match", "passwordMinLength": "Password must be at least 8 characters.", "nameMinLength": "Name must be at least 2 characters.", "validEmail": "Please enter a valid email address.", "organizationNameMinLength": "Organization name must be at least 2 characters.", "rememberMe": "Remember me", "signingIn": "Signing in...", "signInSuccessful": "Sign in successful!", "registrationFailed": "Registration failed. Please try again.", "resettingPassword": "Resetting password...", "passwordResetSuccess": "Password reset successfully!", "iAgreeToThe": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "continue": "Continue", "completeSignUp": "Complete Sign Up", "newPassword": "New Password", "createNewPassword": "Create a new password", "confirmNewPassword": "Confirm New Password", "confirmNewPasswordPlaceholder": "Confirm your new password", "setNewPasswordBeforeContinuing": "You need to set a new password before continuing.", "verifyEmailBeforeSignIn": "Please verify your email before signing in", "signInFailedCheckCredentials": "Sign in failed. Please check your credentials.", "signInFailedTryAgain": "Sign in failed. Please try again.", "registrationError": "Registration error: {error}"}, "organization": {"createOrganization": "Create Organization", "createDescription": "Add a new organization to collaborate with your team.", "descriptionOptional": "Description (Optional)", "urlOptional": "Website URL (Optional)", "creating": "Creating...", "createSuccess": "Organization created successfully", "createFailed": "Failed to create organization", "switching": "Switching organization...", "switchSuccess": "Organization switched successfully", "switchFailed": "Failed to switch organization", "notFound": "Organization not found", "searchPlaceholder": "Search organization...", "noOrganizationsFound": "No organization found.", "general": "General", "members": "Members", "invitations": "Invitations", "organizationInfo": "Organization Information", "updateDetails": "Update your organization details and profile.", "nameLabel": "Name", "namePlaceholder": "Acme Inc.", "nameDescription": "The name of your organization.", "slugLabel": "Slug", "slugPlaceholder": "acme", "slugDescription": "The URL-friendly identifier for your organization.", "descriptionLabel": "Description", "descriptionPlaceholder": "Brief description of your organization", "descriptionHelp": "Describe what your organization does.", "urlLabel": "Website URL", "urlPlaceholder": "https://example.com", "urlDescription": "Your organization's website address.", "saving": "Saving...", "saveChanges": "Save Changes", "teamMembers": "Team Members", "manageTeamMembers": "Manage your team members and their roles.", "inviteUser": "Invite User", "pendingInvitations": "Pending Invitations", "managePendingInvitations": "Manage invitations sent to new team members.", "noInvitations": "No pending invitations", "whenInviteMembers": "When you invite members, they will appear here.", "creatingOrganization": "Creating organization...", "tellUsAboutOrganization": "Tell us about your organization", "nameMinLength": "Organization name must be at least 2 characters.", "slugMinLength": "Slug must be at least 2 characters.", "validUrl": "Please enter a valid URL", "settings": "Organization Settings", "settingsDescription": "Manage your organization settings and preferences.", "loading": "Loading Organization...", "selectFromSidebar": "If this page doesn't load, please select an organization from the dropdown in the sidebar.", "updateFailed": "Failed to update organization", "updating": "Updating company...", "updateSuccess": "Company updated successfully", "slugFormat": "Slug can only contain lowercase letters, numbers, and hyphens."}, "socialAccount": {"disconnectConfirm": "Are you sure you want to disconnect your {platform} account?", "disconnectWarning": "Disconnecting your account will revoke access to the {platform} authentication.", "connectAccount": "Connect Account", "disconnectAccount": "Disconnect Account", "connected": "Connected", "disconnected": "Disconnected"}, "roles": {"owner": "Owner", "admin": "Admin", "member": "Member", "makeAdmin": "Make Admin", "makeMember": "Make Member", "removeMember": "Remove Member", "confirmRemove": "Are you sure you want to remove {name} from this organization? They will lose access to all resources.", "role": "Role", "joined": "Joined", "currentUser": "Current User", "removeTeamMember": "Remove team member", "roleUpdatedSuccess": "Role updated successfully to {role}", "updateRoleFailed": "Failed to update role", "memberRemovedSuccess": "Member removed successfully", "removeMemberFailed": "Failed to remove member", "title": "Roles", "customRoles": "Custom Roles", "customRolesDescription": "Create and manage custom roles with specific permissions", "createRole": "Create Role", "editRole": "Edit Role", "createRoleDescription": "Create a new custom role with specific permissions", "editRoleDescription": "Edit this custom role and its permissions", "deleteRoleTitle": "Delete Role", "deleteRoleDescription": "This action cannot be undone. This will permanently delete this role.", "noRoles": "No custom roles created yet", "permissionsCount": "permissions", "permissions": "Permissions", "customRole": "Custom Role", "selectCustomRole": "Select Custom Role", "selectRole": "Select a role", "ownerOnly": "Only owners can manage custom roles", "name": "Name", "description": "Description"}, "vectordb": {"providerConfiguration": "Provider Configuration", "provider": "Vector Database Provider", "selectProvider": "Select a provider", "mongodbVector": "MongoDB Vector", "chooseProvider": "Choose your vector database provider.", "connectionString": "Connection String", "connectionStringPlaceholder": "********************************:port", "connectionStringDescription": "Your MongoDB connection string including credentials.", "databaseName": "Database Name", "databaseNamePlaceholder": "your_database_name", "databaseNameDescription": "The name of your MongoDB database.", "collectionName": "Collection Name", "collectionNamePlaceholder": "your_collection_name", "collectionNameDescription": "The name of your MongoDB collection for vector storage.", "saveChanges": "Save changes", "updateSuccess": "VectorDB settings updated successfully", "updateError": "Error updating VectorDB settings", "providerRequired": "Provider is required", "connectionStringRequired": "Connection string is required", "databaseNameRequired": "Database name is required", "collectionNameRequired": "Collection name is required"}, "workspace": {"page": "Page", "contentImportedAsHtml": "The content will be imported as HTML.", "deleteFolder": "Delete Folder", "deleteFile": "Delete File", "details": "Workspace Details", "nameRequired": "Workspace name is required", "newFolderName": "New folder name", "create": "Create New Workspace", "pages": "Pages", "creating": "Creating...", "createWorkspace": "Create Workspace", "detailsDescription": "Fill in the information below to create a new workspace.", "description": "Description", "descriptionPlaceholder": "Describe what this workspace is for", "members": "Members", "initialsDescription": "These will be displayed as the workspace icon.", "initialsPlaceholder": "AB", "initials": "Workspace Initials (2 letters)", "namePlaceholder": "Enter workspace name", "workspace": "Workspace", "initialsRequired": "Initials are required", "workspaceMembers": "Workspace Members", "inviteUser": "Invite User", "inviteUserSubtitle": "Invite a user to join your workspace", "addMember": "Add Member", "addRemoveMember": "Add/Remove Member", "allPages": "All Pages", "openPage": "Open Page", "sendingInvitation": "Sending invitation...", "invitationSentSuccess": "Invitation sent successfully", "failedToSendInvitation": "Failed to send invitation", "removingMember": "Removing member...", "memberRemovedSuccess": "Member removed successfully", "failedToRemoveMember": "Failed to remove member. Please try again.", "noWorkspacesYet": "No workspaces yet", "createFirstWorkspace": "Create your first workspace to start organizing your projects and collaborating with your team.", "newPage": "New Page", "searchPages": "Search pages...", "created": "Created", "noPagesFound": "No pages found. Create a new page to get started.", "createNewPage": "Create New <PERSON>", "renamePage": "<PERSON><PERSON>", "enterNewPageName": "Enter a new name for your page.", "enterPageName": "Enter a name for your new page.", "pageName": "Page Name", "enterPageNamePlaceholder": "Enter page name", "syncFolder": "Sync Folder", "syncSharePointFolder": "Sync SharePoint Folder", "syncWithSharePoint": "Sync With SharePoint", "syncWithGoogleDrive": "Sync With Google Drive", "removeSync": "Remove Sync", "newFileName": "New file name", "createAndSync": "Create and Sync", "createNewFolder": "Create New Folder", "folderName": "Folder Name", "close": "Close", "creatingAndSyncingFolder": "Creating and syncing folder...", "folderCreatedAndSyncedSuccess": "Folder created and synced successfully", "errorCreatingFolder": "Error creating folder", "syncingFolder": "Syncing folder...", "folderSyncedSuccess": "Folder synced successfully", "errorSyncingFolder": "Error syncing folder", "connectAccountInSettings": "Please connect your account in integration settings", "removingSync": "Removing sync...", "syncRemovedSuccess": "Sync removed successfully", "failedToRemoveSync": "Failed to remove sync", "checkingSyncStatus": "Checking sync status...", "filesAndFoldersInSync": "All files and folders are in sync with {provider}", "synchronizedFiles": "Synchronized {count} files with {provider}", "folderSyncedSuccessfully": "{provider} folder synced successfully", "failedToSyncFolder": "Failed to sync folder", "googleDrive": "Google Drive", "sharePoint": "SharePoint", "oneDrive": "SharePoint", "googleDriveAndOneDrive": "Google Drive & SharePoint", "sync": "Sync", "pageCreatedSuccess": "Page created successfully!", "failedToCreatePage": "Failed to create page. Please try again.", "pageDeletedSuccess": "Page deleted successfully!", "failedToDeletePage": "Failed to delete page. Please try again.", "loading": "Loading...", "syncedWithCloud": "This page is synced with {provider} and is in read-only mode. To make changes, remove the sync from the Pages section.", "new": "New", "newFolder": "New Folder", "upload": "Upload", "uploadFiles": "Upload Files", "uploadFilesDescription": "Select files from your device to upload to this page.", "cancel": "Cancel", "searchFilesAndFolders": "Search files and folders...", "listView": "List view", "gridView": "Grid view", "tileView": "Tile view", "allItems": "All Items", "folderCount": "{count} folder", "foldersCount": "{count} folders", "fileCount": "{count} file", "filesCount": "{count} files", "name": "Name", "modified": "Modified", "type": "Type", "size": "Size", "actions": "Actions", "folder": "Folder", "file": "File", "rename": "<PERSON><PERSON>", "share": "Share", "delete": "Delete", "readOnly": "(Read-only)", "noItemsFound": "No items found. Create a folder or upload files to get started.", "folders": "Folders", "files": "Files", "openFolder": "Open Folder", "viewFile": "View File", "renameFolder": "<PERSON><PERSON>", "renameFile": "Rename File", "enterFolderName": "Enter a name for your folder.", "enterFileName": "Enter a name for your file.", "createFolder": "Create Folder", "enterFolderNamePlaceholder": "Enter folder name", "enterFileNamePlaceholder": "Enter file name", "loadingFolder": "Loading folder...", "folderLoadingError": "If this message persists, the folder may not exist or there might be an issue loading the data.", "returnToWorkspace": "Return to Workspace", "searchInFolder": "Search in this folder...", "uploadToFolder": "Select files from your device to upload to this folder.", "loadingFile": "Loading...", "back": "Back", "lastModified": "Last modified: {date}", "more": "More", "downloadFile": "Download File", "downloadDocument": "Download Document", "viewRawMarkdown": "View Raw Markdown", "downloadTextFile": "Download Text File", "downloadSpreadsheet": "Download Spreadsheet", "downloadPresentation": "Download Presentation", "downloadJsonFile": "Download JSON File", "downloadHtmlFile": "Download HTML File", "downloadCsvFile": "Download CSV File", "loadingDocumentContent": "Loading document content...", "loadingMarkdownContent": "Loading markdown content...", "loadingTextContent": "Loading text content...", "loadingSpreadsheetContent": "Loading spreadsheet content...", "loadingPresentationContent": "Loading presentation content...", "loadingJsonContent": "Loading JSON content...", "loadingHtmlContent": "Loading HTML content...", "loadingCsvContent": "Loading CSV content...", "documentNotAvailable": "Document not available", "documentCouldNotBeLoaded": "The document could not be loaded", "textFileNotAvailable": "Text file not available", "spreadsheetNotAvailable": "Spreadsheet not available", "presentationNotAvailable": "Presentation not available", "jsonFileNotAvailable": "JSON file not available", "htmlFileNotAvailable": "HTML file not available", "csvFileNotAvailable": "CSV file not available", "fileCouldNotBeLoaded": "The file could not be loaded", "fileTypeCannotBePreviewedTitle": "This file type cannot be previewed", "fileTypeCannotBePreviewedDesc": "The file format {extension} is not supported for preview.", "openInNewTab": "Open in new tab", "redirectingToWorkspace": "Redirecting to workspace page...", "loadingWorkspaceMessage": "Please wait while we load your workspace.", "addRemoveMembers": "Add/Remove members", "viewDetails": "View details", "appName": "Swiss Knowledge Hub", "workspaceDetails": "Workspace Details", "manageWorkspaceDetails": "Manage your workspace details", "updateSuccess": "Workspace updated successfully", "updateFailed": "Failed to update workspace", "errorFetchingDetails": "Error fetching workspace details", "workspaceNotFound": "Workspace not found", "noAccess": "No access", "vectorizationStatusTooltip": "Vectorization status indicates whether the file has been successfully indexed for AI search.", "importFromUrl": "Import from URL", "importFromUrlDescription": "Paste a URL to import content from a web page", "urlImportSuccess": "Content imported successfully", "urlImportError": "Failed to import content", "urlAlreadyImported": "This URL has already been imported to this workspace", "extractImages": "Extract images", "contentCleaningLevel": "Content cleaning level", "cleaningLevelBasic": "Basic (keep all content)", "cleaningLevelMedium": "Medium (remove short paragraphs)", "cleaningLevelAggressive": "Aggressive (remove all non-essential content)", "crawlDepth": "Crawl depth", "crawlDepth1": "Single page only", "crawlDepth2": "Include linked pages (1 level)", "crawlDepth3": "Deep crawl (2 levels)", "sitemapUrls": "Sitemap URLs", "selectUrlsToImport": "Select URLs to import from the sitemap", "batchImport": "Import {{count}} selected URLs", "batchImportSuccess": "Successfully imported {{count}} pages", "batchImportPartialFailure": "Failed to import {{count}} pages", "batchImportFailure": "Failed to import any pages", "batchImportError": "Error during batch import", "noUrlsSelected": "No URLs selected for import", "extractedImages": "Extracted images", "useBackgroundProcessing": "Use background processing", "backgroundProcessingDescription": "Process URL import in the background to avoid waiting", "importStatus": "Import Status", "statusPending": "Pending", "statusProcessing": "Processing", "statusCompleted": "Completed", "statusFailed": "Failed", "retryImport": "Retry Import", "urlImportStarted": "URL import started in the background"}, "settings": {"organization": {"title": "Organization", "description": "Manage your organization's profile and settings."}, "members": {"title": "Team Members", "description": "Manage your organization's team members."}, "roles": {"title": "Roles", "description": "Manage custom roles and permissions"}, "groups": {"title": "Groups", "description": "Manage groups and workspace access"}, "embedded": {"title": "Embedded Provider", "description": "Configure your embedded provider settings"}, "llm": {"title": "LLM Provider", "description": "Configure your LLM provider settings"}, "vectordb": {"title": "VectorDB Provider", "description": "Configure your vectorDB provider settings"}, "integrations": {"comingSoon": "Coming Soon", "selectSharePointSite": "Select SharePoint Site", "selectSharePointFolder": "Select SharePoint Folder", "title": "Storage Integrations", "description": "Configure your storage integrations"}}, "embedded": {"providerConfiguration": "Provider Configuration", "provider": "Provider", "selectProvider": "Select a provider", "apiKey": "API Key", "modelName": "Model Name", "deploymentName": "Deployment Name", "endpoint": "API Endpoint", "enterApiKey": "**********", "enterModelName": "embed-v-4-0", "enterDeploymentName": "******", "enterEndpoint": "https://openai.azure.com/openai/deployments/******/chat/completions", "saving": "Saving...", "saveChanges": "Save Changes", "updateSuccess": "Embedded settings updated successfully", "updateFailed": "Failed to update embedded settings"}, "inviteMember": {"inviteMember": "Invite member", "inviteTeamMember": "Invite team member", "inviteDescription": "Invite a new member to join your organization.", "emailAddress": "Email address", "emailPlaceholder": "<EMAIL>", "selectRolePlaceholder": "Select a role", "sending": "Sending...", "sendInvitation": "Send invitation", "invitationSent": "Invitation sent to {email}", "invitationFailed": "Failed to send invitation. Please try again.", "validEmail": "Please enter a valid email address.", "selectRole": "Please select a role."}, "memberList": {"teamMembers": "Team Members", "manageTeamMembers": "Manage your team members", "noTeamMembers": "No team members yet", "removeTeamMember": "Remove Team Member", "confirmRemove": "Are you sure you want to remove {name} from your tenant? This action cannot be undone.", "removing": "Removing...", "roleChangeSuccess": "Changed {name}'s role to {role}", "roleChangeFailed": "Failed to update member role", "memberRemoveSuccess": "Removed {name} from the tenant", "memberRemoveFailed": "Failed to remove member"}, "chatHistory": {"title": "Chat History", "noHistoryYet": "No chat history yet", "startNewChatPrompt": "Start a new chat to begin your conversation", "startNewChat": "Start New Chat", "renameSuccess": "Chat renamed successfully", "renameFailed": "Failed to rename chat", "deleteSuccess": "<PERSON><PERSON> deleted successfully", "deleteFailed": "Failed to delete chat"}, "theme": {"toggleTheme": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "groups": {"title": "Groups", "groups": "Groups", "searchGroups": "Search groups...", "group": "Group", "createGroup": "Create Group", "createNewGroup": "Create New Group", "createGroupDescription": "Create a new group to manage access to workspaces", "groupName": "Group Name", "enterGroupName": "Enter group name", "description": "Description", "enterDescription": "Enter description (optional)", "groupCreated": "Group created successfully", "groupCreateFailed": "Failed to create group", "noGroupsYet": "No groups yet", "createFirstGroup": "Create your first group to manage workspace access", "members": "Members", "workspaces": "Workspaces", "groupMembers": "Group Members", "addUser": "Add User", "noMembers": "No members in this group yet", "noMembersYet": "No members yet", "addMembersDescription": "Add members to this group to give them access to all assigned workspaces", "assignedWorkspaces": "Assigned Workspaces", "assignWorkspace": "Assign Workspace", "noWorkspaces": "No workspaces assigned to this group yet", "noWorkspacesYet": "No workspaces yet", "assignWorkspacesDescription": "Assign workspaces to this group to give all members access", "addUserToGroup": "Add User to Group", "addUserToGroupDescription": "Add a user to this group. They will gain access to all workspaces assigned to this group.", "userAddedToGroup": "User added to group successfully", "failedToAddUserToGroup": "Failed to add user to group", "removeUserConfirm": "Remove User from Group", "removeUserDescription": "Are you sure you want to remove this user from the group? They will lose access to all workspaces assigned to this group unless they have direct access.", "userRemovedFromGroup": "User removed from group successfully", "failedToRemoveUser": "Failed to remove user from group", "assignWorkspaceToGroup": "Assign Workspace to Group", "assignWorkspaceDescription": "Assign a workspace to this group. All members of the group will gain access to this workspace.", "selectWorkspace": "Select Workspace", "noWorkspacesFound": "No workspaces found", "workspaceAssignedToGroup": "Workspace assigned to group successfully", "failedToAssignWorkspace": "Failed to assign workspace to group", "removeWorkspaceConfirm": "Remove Workspace from Group", "removeWorkspaceDescription": "Are you sure you want to remove this workspace from the group? Group members will lose access to this workspace unless they have direct access.", "workspaceRemovedFromGroup": "Workspace removed from group successfully", "failedToRemoveWorkspace": "Failed to remove workspace from group", "loadingGroups": "Loading groups...", "failedToFetchGroups": "Failed to fetch groups", "manageAccessDescription": "Create and manage groups to control workspace access", "selectGroupPrompt": "Select a group", "selectGroupDescription": "Choose a group from the list to view and manage its members and workspaces", "noDescription": "No description"}, "memberSidebar": {"newChat": "New Chat", "chatHistory": "Chat History", "group": "Group", "groups": "Groups", "createGroup": "Create Group", "editGroup": "Edit Group", "deleteGroup": "Delete Group", "confirmDelete": "Are you sure you want to delete this group?", "groupDeleted": "Group deleted successfully", "groupDeleteFailed": "Failed to delete group", "createNewChatGroup": "Create New Chat Group", "createGroupDescription": "Create a new chat group to organize your chats.", "groupName": "Group Name", "enterGroupName": "Enter group name", "groupCreated": "Group created successfully", "groupCreateFailed": "Failed to create group"}, "sidebar": {"dashboard": "Dashboard", "myHub": "My Hub", "askAi": "Ask AI", "chatHistory": "Chat History", "support": "Support", "upgrade": "Upgrade", "managePages": "Manage Pages", "addWorkspace": "+Add", "help": "Documentation", "toggleSidebar": "Toggle Sidebar", "privacyPolicy": "Privacy Policy", "sharedThreads": "Shared Threads"}, "chat": {"askAi": "Ask AI", "enterMessage": "Enter a message", "errorProcessingRequest": "Sorry, there was an error processing your request.", "renaming": "Renaming chat...", "renameSuccess": "Chat renamed successfully", "renameFailed": "Failed to rename chat", "moving": "Moving chat...", "moveSuccess": "<PERSON><PERSON> moved successfully", "moveFailed": "Failed to move chat", "removing": "Removing chat...", "removeSuccess": "Chat removed successfully", "removeFailed": "Failed to remove chat", "reordering": "Reordering chat...", "reorderSuccess": "Chat reordered successfully", "reorderFailed": "Failed to reorder chat", "moveChatDescription": "Select a group to move this chat to.", "selectGroup": "Select group.", "moveToGroup": "Move to Group", "like": "Like this response", "dislike": "Dislike this response", "regenerate": "Regenerate response", "sources": "Sources", "copyMessage": "Copy message", "copiedToClipboard": "Copied to clipboard", "viewSource": "View source", "feedbackLike": "Thank you for your positive feedback!", "feedbackDislike": "Thank you for your feedback. We'll work to improve.", "feedbackError": "Error saving feedback. Please try again.", "regenerating": "Regenerating response...", "regenerateError": "Error regenerating response. Please try again.", "originalResponse": "Original response", "regeneratedResponse": "Regenerated response", "regeneratedFrom": "Regenerated from original", "regenerationHistory": "Response Comparison", "previousResponse": "Previous response", "nextResponse": "Next response", "viewRegeneratedResponses": "View regenerated responses", "viewOriginalResponse": "View original response", "showLatestResponse": "Show latest response", "showOriginalResponse": "Show original response", "openDocument": "Open document", "page": "Page", "documentPreview": "Document Preview", "regenerateLastOnly": "You can only regenerate the last assistant message", "citationsAccordion": "Citations", "relevance": "Relevance", "includeWebResults": "Include Public Web Results", "webSearchTooltip": "Include results from the public web to enhance answers", "webSearchLimitExceeded": "Daily web search limit exceeded. Please try again tomorrow.", "webSearchError": "Web search error", "webSearchErrorDescription": "There was an error performing the web search. Please try again later.", "tryAgainTomorrow": "Please try again tomorrow.", "documentSources": "Documents", "webSources": "Web", "thinking": "Thinking...", "uploadImage": "Upload image", "invalidImageFormat": "Invalid image format. Please use JPG, PNG, or WebP.", "imageTooLarge": "Image size should be less than 10MB.", "tooManyImages": "Maximum 5 images allowed.", "imageProcessingError": "Error processing image.", "dropImagesHere": "Drop images here", "removeImage": "Remove image", "imageUploadHelp": "Drag & drop images or click to upload (JPG, PNG, WebP, max 10MB)", "enterMessageWithImages": "Ask about your images or add a message...", "download": "Download", "openInNewTab": "Open", "imagesAttached": "Images attached", "imagesCount": "images", "shown": "shown", "attachedImages": "Attached Images", "moreImages": "more images"}, "quickAsk": {"buttonLabel": "Quick Ask", "placeholder": "Ask a quick question...", "thinking": "Thinking...", "emptyState": "Ask a question to get a quick answer", "openAsChat": "Open as <PERSON><PERSON>", "clearConversation": "Clear Conversation"}, "integration": {"comingSoon": "Coming Soon", "oneDriveDescription": "Instantly Connect your SharePoint sites to sync your files.", "sharePointDescription": "Instantly Connect your SharePoint sites to sync your files.", "googleDriveDescription": "Instantly Connect your Google Drive Workspace to sync your files.", "connected": "Connected", "notConnected": "Not connected", "connect": "Connect", "disconnect": "Disconnect", "configure": "Configure", "manage": "Manage", "selectSite": "Select SharePoint Site", "selectFolder": "Select SharePoint Folder", "selectSharePointSite": "Select SharePoint Site", "selectSharePointFolder": "Select SharePoint Folder", "noSitesFound": "No sites found", "noFoldersFound": "No folders found", "syncing": "Syncing...", "syncWithSharePoint": "Sync with SharePoint", "syncComplete": "Sync complete! {count} files synced.", "selectedSite": "Selected Site", "selectedFolder": "Selected Folder", "syncDisclaimer": "All files and subfolders within the selected folder will be synchronized and available in read-only mode.", "root": "Root"}, "password": {"currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "placeholder": "••••••••", "requirements": "Password must be at least 8 characters long.", "updateButton": "Update Password", "updating": "Updating password...", "updateSuccess": "Password updated successfully", "updateFailed": "Failed to update password", "currentPasswordMinLength": "Current password must be at least 6 characters.", "newPasswordMinLength": "New password must be at least 8 characters.", "confirmPasswordMinLength": "Confirm password must be at least 8 characters.", "resetPassword": "Reset Password", "createNewPassword": "Create a new password for your account", "createPasswordPlaceholder": "Create a new password", "confirmPasswordPlaceholder": "Confirm your new password", "resetting": "Resetting your password...", "resetSuccess": "Password successfully reset!", "passwordReset": "Password Reset", "resetSuccessMessage": "Your password has been successfully reset.", "redirectMessage": "You will be redirected to the sign in page in a few seconds.", "verifyingLink": "Verifying your reset link...", "invalidLink": "Invalid Link", "linkExpired": "This password reset link is invalid or has expired.", "requestNewLink": "Request New Link", "tokenVerificationFailed": "Could not verify reset token. Please try again."}, "dashboard": {"welcomeMessage": "Welcome to Swiss Knowledge Hub", "getStartedMessage": "To get started, create your first organization to collaborate with your team.", "dashboardTitle": "Dashboard", "organizationCount": "You are a member of {count} organization", "organizationCountPlural": "You are a member of {count} organizations"}, "profile": {"profileInformation": "Profile Information", "updateProfileDetails": "Update your profile information.", "firstNamePlaceholder": "<PERSON>", "lastNamePlaceholder": "<PERSON><PERSON>", "emailPlaceholder": "<EMAIL>", "deleteAccount": "Delete Account", "deleteAccountWarning": "This action cannot be undone. This will permanently delete your account and remove your data from our servers.", "confirmDeleteAccount": "Yes, delete my account", "deletingAccount": "Deleting account...", "errorLoading": "Error loading profile. Please try again later.", "profileUpdated": "Profile updated successfully", "updateFailed": "Failed to update profile", "accountDeleted": "Account deleted successfully", "deleteFailed": "Failed to delete account", "dangerZone": "Danger Zone", "areYouSure": "Are you absolutely sure?"}, "verification": {"verifyEmail": "Verify Your Email", "sentEmailTo": "We've sent a verification email to {email}", "checkEmail": "Please check your email to verify your account", "instructions": "Before you can access your account, you need to verify your email address. Please check your inbox and click on the verification link in the email we sent you.", "cantFindEmail": "Can't find the email?", "checkSpam": "Check your spam or junk folder", "checkEmailCorrect": "Make sure you entered the correct email address", "allowTime": "Allow a few minutes for the email to arrive", "resendEmail": "Resend Verification Email", "backToSignIn": "Back to Sign In", "emailRequired": "Email address is required", "sendingEmail": "Sending verification email...", "emailSentSuccess": "Verification email sent successfully!", "resendFailed": "Failed to resend verification email. Please try again.", "registrationSuccessful": "Registration successful! Please check your email to verify your account.", "sentVerificationEmailTo": "We've sent a verification email to <strong>{email}</strong>. Please check your inbox and click the verification link to activate your account.", "clickToResend": "Click here to resend the verification email", "goToSignIn": "Go to Sign In"}, "billing": {"Starter": "Starter", "Business": "Business", "Enterprise": "Enterprise", "Custom": "Custom", "total": "Total", "discountCode": "Discount Code", "enterDiscountCode": "Enter discount code", "apply": "Apply", "discountCodeApplied": "Discount code applied successfully", "descriptionStarter": "Perfect for small teams or individuals getting started.", "descriptionBusiness": "Ideal for growing teams with moderate usage needs.", "descriptionEnterprise": "For professional teams with higher usage requirements.", "descriptionCustom": "Custom solution for large organizations with specific needs.", "title": "Billing & Subscription", "subtitle": "Manage your subscription and billing information", "currentSubscription": "Current Subscription", "currentSubscriptionDesc": "Your current plan and usage information", "trialBadge": "Free Trial", "trialEndsOn": "Trial ends on {date}", "plan": "Plan", "includedUsers": "{count} included users", "manageSubscriptionAddons": "Manage Subscription Add-ons", "adjustSubscriptionAddons": "Adjust users and storage for your subscription", "users": "Users", "storage": "Storage", "totalSubscriptionCost": "Total Subscription Cost", "increasingSubscription": "This will increase your monthly cost by CHF {price}", "decreasingSubscription": "Subscription downgrades are not available.", "additionalStorage": "Additional Storage", "additionalStorageGB": "{count} GB additional storage", "noAdditionalStorage": "No additional storage", "included": "Included", "free": "Free", "totalStorage": "Total Storage", "basePlanStorage": "{storage} GB included in plan", "currentUsage": "Current Usage", "exceededBaseStorage": "Exceeded base plan storage", "addingStorage": "Adding {count} GB storage will increase your monthly cost by CHF {price}", "reducingStorage": "Reducing {count} GB storage will decrease your monthly cost by CHF {price}", "manageStorage": "Manage Vector Storage", "adjustStorage": "Adjust your vector database storage", "updateStorageButton": "Update Storage", "additionalStorageFee": "Additional storage available at tiered pricing", "includedStorage": "{count} GB vector storage included", "includedPlusAdditionalStorage": "{included} GB included + {additional} GB additional storage", "subscriptionUpdateSuccess": "Subscription updated successfully", "subscriptionUpdateFailed": "Failed to update subscription", "includedPlusAdditionalUsers": "{included} included users + {additional} additional users", "vectorStoreUsage": "Vector Store Usage", "usageOf": "{used} GB of {total} GB used", "availablePlans": "Available Plans", "choosePlan": "Choose the plan that best fits your needs", "manageSubscription": "Manage Subscription", "currentPlan": "Current Plan", "contactUs": "Contact Us", "subscribe": "Subscribe", "upgradePlan": "Upgrade Plan", "processing": "Processing...", "priceCHF": "CHF {price}", "perMonth": "per month", "perYear": "per year", "monthly": "Monthly", "yearly": "Yearly", "save": "Save", "savePercent": "Save {percent}", "saveCHF": "Save CHF {amount}", "save2Months": "Save 2 months", "additionalUserFee": "+ CHF {fee}.- per additional user", "includes": "Includes", "customNumberOf": "Custom No. of", "usersLabel": "users", "custom": "Custom", "gbVectorStore": "GB vector store", "allFeatures": "All features", "hostingInSwitzerland": "Hosting in Switzerland", "standardSupport": "Standard support", "additionalUsers": "Additional Users", "additionalUsersDescription": "Your {planName} plan includes {includedUsers} users. Would you like to add more?", "summary": "Summary", "basePlan": "Base plan ({users} users)", "additionalUsersCount": "Additional users ({count})", "pricePerUserCHF": "CHF {price} per user", "totalUsers": "Total ({count} users)", "pricePerMonth": "CHF {price}/month", "pricePerYear": "CHF {price}/year", "confirmAndCheckout": "Confirm & Checkout", "noChangesToUpdate": "No changes to update.", "portalOpenFailed": "Failed to open customer portal. Please try again.", "loginRequiredForUpgrade": "You must be logged in to upgrade your plan", "checkoutSessionFailed": "Failed to create checkout session. Please try again.", "subscriptionUpdated": "Your subscription has been updated successfully.", "subscriptionCanceled": "Subscription update was canceled.", "noTenantSelected": "No tenant selected. Please select a tenant to view billing information.", "adjustSubscription": "Adjust users and storage for your subscription", "updateSubscription": "Update Subscription", "addingUsers": "Adding {count} users will increase your monthly cost by CHF {price}", "removingUsers": "Removing {count} users will decrease your monthly cost by CHF {price}", "customerNotFound": "Your Stripe customer account was not found. Please try again or contact support if the issue persists.", "updateUsers": "Update Users", "updateStorageAction": "Update/Add Storage", "addStorageAction": "Add Storage", "existingAdditionalStorage": "Existing additional storage ({count} GB)", "newTotalStorage": "New total additional storage ({count} GB)", "newStorageBeingAdded": "New storage being added ({count} GB)", "totalAdditionalStorage": "Total additional storage ({count} GB)", "currentAdditionalStorage": "Current Additional Storage", "confirmUpdateUsers": "Confirm User Update", "confirmUpdateStorage": "Confirm Storage Update", "confirmAddUsers": "Are you sure you want to add {count} additional users? This will increase your {interval} cost by CHF {price}.", "confirmRemoveUsers": "Are you sure you want to remove {count} users? This will decrease your {interval} cost by CHF {price}.", "confirmAddStorage": "Are you sure you want to add {count} GB of additional storage? This will increase your {interval} cost by CHF {price}.", "confirmRemoveStorage": "Are you sure you want to remove {count} GB of storage? This will decrease your {interval} cost by CHF {price}.", "confirmUpdate": "Confirm Update", "selectStorageIncrement": "Select Storage Increment to Add", "addSelectedStorage": "Add {size} GB Storage", "addingStorageToExisting": "Adding to existing {existing} GB", "resetStorage": "Reset", "invalidStorageTier": "Invalid storage tier selected.", "noStorageTiersSelected": "No storage tiers selected.", "currentStorageTiers": "Current Storage Tiers", "duplicateStorageTier": "Error: Duplicate storage tier detected. Please try a different configuration.", "usersUpdateSuccess": "Users updated successfully", "usersUpdateFailed": "Failed to update users", "storageUpdateSuccess": "Storage updated successfully", "storageUpdateFailed": "Failed to update storage", "processingRequest": "Processing...", "errorFetchingPrices": "Error fetching prices from Stripe. Using fallback prices.", "downgradeNotAllowed": "You cannot downgrade your plan yourself. Please contact support."}, "invitations": {"noPending": "No pending invitations", "inviteMembers": "When you invite members, they will appear here.", "sent": "<PERSON><PERSON>", "expires": "Expires", "copyLink": "Copy invitation link", "resend": "Resend invitation", "cancel": "Cancel invitation", "cancelInvitation": "Cancel invitation", "cancelConfirmation": "Are you sure you want to cancel the invitation sent to {email}? They will no longer be able to join the organization with this invitation.", "keep": "Keep", "cancelSuccess": "Invitation cancelled successfully", "cancelError": "Failed to cancel invitation", "resendSuccess": "Invitation resent successfully", "resendError": "Failed to resend invitation", "linkCopied": "Invitation link copied to clipboard"}, "llmSettings": {"providerConfiguration": "Provider Configuration", "provider": "Provider", "selectProvider": "Select a provider", "chooseProvider": "Choose your LLM provider", "apiKey": "API Key", "enterApiKey": "**********", "apiKeyDescription": "Your {provider} API key", "apiKeyRequired": "API Key is required", "modelName": "Model Name", "enterModelName": "DeepSeek-V3", "modelDescription": "The name of the model to use (e.g., gpt-4)", "modelRequired": "Model name is required", "azureEndpoint": "Azure Endpoint", "enterAzureEndpoint": "Enter Azure endpoint URL", "azureEndpointDescription": "Your Azure OpenAI service endpoint URL", "azureDeploymentName": "Azure Deployment Name", "enterDeploymentName": "Enter deployment name", "deploymentDescription": "The name of your Azure OpenAI deployment", "saveSettings": "Save Settings"}, "subscription": {"noActiveSubscription": "No Active Subscription", "noSubscriptionMessage": "You don't have an active subscription. You won't be able to create workspaces or invite members.", "subscribePlan": "Subscribe to a Plan", "workspaceCreationRestricted": "You need an active subscription to create workspaces", "memberInvitationRestricted": "You need an active subscription to invite members", "vectorStorageWarning": "Warning: This upload will exceed your vector database storage limit by approximately {willExceedBy} GB.", "vectorStorageUsage": "Current usage: {currentUsageGB} GB of {limitGB} GB.", "vectorStorageContinue": "Do you want to continue with the upload?", "userLimitReached": "User Limit Reached", "userLimitMessage": "You have reached your plan's user limit ({currentCount}/{limit}). Please upgrade your plan or remove existing members before inviting new ones."}, "api": {"errors": {"failedToUploadFile": "Failed to upload file", "failedToUpdateFile": "Failed to update file", "failedToDeleteFile": "Failed to delete file", "fileNotFound": "File not found", "unauthorized": "Unauthorized - userId is required", "fileIdRequired": "File ID is required", "fileIdAndNameRequired": "File ID and name are required"}}, "privacyPolicy": {"title": "Privacy Policy", "scope": "<PERSON><PERSON>", "scopeContent": "This Privacy Policy applies to the SaaS platform Swiss Knowledge Hub and all related web and API services.", "preamble": "Preamble", "preambleContent": "Swiss Knowledge Hub GmbH (\"we\", \"us\") processes personal data in accordance with the Swiss Federal Act on Data Protection (FADP) and its implementing ordinance.", "controller": "Controller", "controllerContent": "Swiss Knowledge Hub GmbH\nKönizstrasse 161\n3097 Liebefeld, Switzerland\nUID CHE-219.860.\n<EMAIL> | +41 31 318 33 55\nRepresented by <PERSON>, Member of the Board", "purposes": "Purposes of Processing", "purposesContent1": "1. Provision and operation of the platform", "purposesContent2": "2. Management of user accounts and access rights", "purposesContent3": "3. Communication and support", "purposesContent4": "4. Payment processing and subscription management", "purposesContent5": "5. System security, abuse prevention, log analysis", "purposesContent6": "6. Compliance with legal obligations and the establishment, exercise or defence of legal claims", "categories": "Categories of Data", "categoriesContent": "- Master data such as name, business email, company affiliation\n- Authentication data such as hashed passwords, JWT tokens, session IDs\n- Content data such as uploaded or synchronised documents and messages\n- Payment data such as transaction and subscription identifiers\n- Usage and log data such as IP address, timestamps, API calls", "legalBases": "Legal Bases", "legalBasesContent": "- Contract performance or steps prior to entering into a contract\n- Legitimate interests in secure and efficient operation\n- Consent, which can be withdrawn at any time with future effect", "retention": "Retention", "retentionContent": "Data is erased or anonymised once it is no longer required for the stated purposes. Account and content data are removed no later than twelve months after contract termination unless statutory retention duties require longer storage.", "recipients": "Recipients and Processors", "recipientsContent1": "- **Microsoft Azure Switzerland North** – full hosting of application, database, storage and email.", "recipientsContent2": "- **Stripe Payments Europe Ltd. (Ireland) / Stripe Inc. (USA)** – payment processing.", "recipientsContent3": "- **No** additional external providers outside Switzerland other than specified in section 7b. All processors are contractually bound to confidentiality.", "ai": "Use of Artificial Intelligence", "aiContent1": "We employ machine-learning algorithms (LangChain-based retrieval-augmented generation) for semantic search and document analysis.", "aiContent2": "- Processing is confined to Azure data centres in Switzerland.", "aiContent3": "- No automated decisions producing legal or similarly significant effects are made.", "aiContent4": "- Vector embeddings are pseudonymised and stored in encrypted form.", "thirdParty": "Third-Party Integrations (SharePoint, Google Drive)", "thirdPartyContent": "If a customer activates the optional Microsoft SharePoint or Google Drive connector, selected files are synchronised directly via the respective provider. These providers process data under their own privacy terms and may do so in countries outside Switzerland. Configuration of the integrations is the customer's responsibility.", "transfers": "Cross-Border Transfers", "transfersContent": "The platform is operated in Switzerland. Payment-related data are transmitted to Stripe, which may process them in the United States. Transfers rely on the EU Standard Contractual Clauses (SCC 2021) pursuant to Art. 16 FADP.", "security": "Data Security", "securityContent": "- End-to-end TLS encryption for all transmissions\n- Encryption of sensitive database fields\n- Password storage using bcrypt\n- Role-based access control\n- Logging of security-relevant events and regular audits", "rights": "Data Subject Rights", "rightsContent": "Individuals have the right to access, rectification, erasure, restriction of processing, data portability, withdrawal of consent and to lodge a complaint with the Swiss Federal Data Protection and Information Commissioner. Requests must be sent to the address in section 2; proof of identity may be required.", "cookies": "Cookies and Similar Technologies", "cookiesContent1": "We use only essential cookies.", "cookiesContent2": "- Session cookie: HTTP-Only, Secure, SameSite Lax, expires after a maximum of 24 h\n- Language preference cookie: expires with the session\n- Subscription status cookie: expires after 30 days", "cookiesContent5": "No marketing or tracking cookies are deployed.", "minors": "Protection of Minors", "minorsContent": "The platform is intended solely for users aged 16 years or older.", "changes": "Changes to this Policy", "changesContent": "We may amend this policy at any time. The version published on the platform is authoritative. Users will be notified of material changes inside the application or by email.", "disclaimer": "Disclaimer", "disclaimerContent": "Information is reviewed regularly yet may change over time. Liability is excluded to the extent permitted by law.", "contact": "Contact", "contactContent": "<EMAIL> | +41 31 318 33 55\nSwiss Knowledge Hub GmbH, Könizstrasse 161, 3097 Liebefeld, Switzerland", "version": "Version 1.1, April 2025", "overview": "Overview", "detailedPolicy": "Detailed Policy", "dataCollection": "Data Collection", "dataCollectionDesc": "We collect only essential data needed to provide our services, including account information, content data, and usage logs.", "dataSecurity": "Data Security", "dataSecurityDesc": "We employ end-to-end encryption, secure password storage, and role-based access control to protect your data.", "dataProcessing": "Data Processing", "dataProcessingDesc": "All data is processed in Switzerland with limited third-party processors bound by confidentiality agreements.", "cookiesTitle": "Cookies", "cookiesDesc": "We use only essential cookies for session management, language preferences, and subscription status.", "keyPoints": "Key Points", "keyPoint1": "All data is hosted in Microsoft Azure Switzerland North data centers.", "keyPoint2": "Payment processing is handled by Stripe, which may process data in the US.", "keyPoint3": "You have rights to access, rectify, erase, and restrict processing of your data.", "keyPoint4": "The platform is intended for users aged 16 years or older."}}