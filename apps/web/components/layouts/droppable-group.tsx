"use client";

import * as React from "react";
import { useDroppable } from "@dnd-kit/core";
import { ChevronRight, Folder } from "lucide-react";
import { DraggableChatItem } from "./draggable-chat-item";
import { cn } from "@/lib/utils";

interface DroppableGroupProps {
  group: any;
  chats: any[];
  isActive: boolean;
  isExpanded: boolean;
  toggleGroupExpansion: (groupName: string) => void;
  editingIndex: number | null;
  editingName: string;
  setEditingName: (name: string) => void;
  handleNameSubmit: (chat: any) => void;
  setEditingIndex: (index: number | null) => void;
  openDropdown: number | null;
  setOpenDropdown: (index: number | null) => void;
  hoveredProject: number | null;
  setHoveredProject: (index: number | null) => void;
  groups: any[];
  handleStartEditing: (index: number, name: string) => void;
  router: any;
  isChatActive: (chatId: string) => boolean;
  isOver?: boolean;
}

export function DroppableGroup({
  group,
  chats,
  isActive,
  isExpanded,
  toggleGroupExpansion,
  editingIndex,
  editingName,
  setEditingName,
  handleNameSubmit,
  setEditingIndex,
  openDropdown,
  setOpenDropdown,
  hoveredProject,
  setHoveredProject,
  groups,
  handleStartEditing,
  router,
  isChatActive,
  isOver = false,
}: DroppableGroupProps) {
  const { setNodeRef } = useDroppable({
    id: `group-${group.id}`,
    data: {
      type: "group",
      groupId: group.id,
    },
  });

  const groupName = group.name;

  return (
    <li key={group.id} className="group/menu-item relative">
      <div className="flex items-center">
        <div className="flex w-full">
          <div
            className={cn(
              "flex flex-1 items-center gap-2 overflow-hidden rounded-l-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
              isActive
                ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                : "",
              isOver && "bg-sidebar-accent/30 ring-2 ring-primary/50"
            )}
          >
            <div
              onClick={() => toggleGroupExpansion(groupName)}
              className="flex-1 overflow-hidden"
            >
              <span
                className="overflow-hidden text-ellipsis whitespace-nowrap w-full inline-block"
                title={group.name}
              >
                {group.name}
              </span>
            </div>
          </div>
          <button
            onClick={() => toggleGroupExpansion(groupName)}
            className={cn(
              "flex items-center overflow-hidden rounded-r-md p-2 text-left text-sm hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
              isActive
                ? "bg-sidebar-accent/50 text-sidebar-accent-foreground"
                : "",
              isOver && "bg-sidebar-accent/30"
            )}
          >
            <ChevronRight
              className={cn(
                "size-4 shrink-0 transition-transform",
                isExpanded ? "rotate-90" : ""
              )}
            />
          </button>
        </div>
      </div>

      {/* Expanded Chat List */}
      {isExpanded && (
        <div
          ref={setNodeRef}
          className={cn(
            "ml-6 mt-1 space-y-1 min-h-[20px] rounded-md transition-colors",
            isOver && "bg-primary/5 ring-1 ring-primary/20"
          )}
        >
          {chats.length > 0 &&
            chats.map((chat: any, chatIndex: number) => {
              const chatIsActive = isChatActive(chat?.id);

              return (
                <DraggableChatItem
                  key={chat.id}
                  chat={chat}
                  chatIndex={chatIndex}
                  groupId={group.id}
                  isActive={chatIsActive}
                  editingIndex={editingIndex}
                  editingName={editingName}
                  setEditingName={setEditingName}
                  handleNameSubmit={handleNameSubmit}
                  setEditingIndex={setEditingIndex}
                  openDropdown={openDropdown}
                  setOpenDropdown={setOpenDropdown}
                  hoveredProject={hoveredProject}
                  setHoveredProject={setHoveredProject}
                  groups={groups}
                  handleStartEditing={handleStartEditing}
                  router={router}
                />
              );
            })}

          {/* Drop indicator when empty */}
          {chats.length === 0 && isOver && (
            <div className="p-3 text-xs text-primary text-center border-2 border-dashed border-primary/50 rounded-md bg-primary/5 animate-pulse">
              <div className="flex items-center justify-center gap-2">
                <Folder className="w-4 h-4 text-primary" />
                Drop chat here
              </div>
            </div>
          )}

          {/* Drop indicator when dragging over with items */}
          {chats.length > 0 && isOver && (
            <div className="absolute inset-0 border-2 border-dashed border-primary/30 rounded-md bg-primary/5 pointer-events-none">
              <div className="flex items-center justify-center h-full">
                <div className="text-xs text-primary font-medium bg-white/90 px-2 py-1 rounded">
                  Drop to add to group
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </li>
  );
}
