import { apiUrl, fetchJson } from "..";

export const createChat = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/id`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "Create Chat api calling error": error });
    return { error: "Error Creating The Chat" };
  }
};

export const updateChat = async (data) => {
  try {
    const response = await fetch(`${apiUrl}/chat/${data.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update chats api calling error": error });
    return { error: "Error Update The Chat" };
  }
};

export const updateChatOrder = async (data: {
  id: string;
  order: number;
  groupId?: string | null;
}) => {
  try {
    const response = await fetch(`${apiUrl}/chat/${data.id}/order`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "update chat order api calling error": error });
    return { error: "Error updating chat order" };
  }
};

export const getChat = async ({ id = "", userId, tenantId }) => {
  try {
    if (id) {
      const response = await fetchJson(
        `${apiUrl}/chat/${id}?id=${id}&userId=${userId}&tenantId=${tenantId}`
      );

      return response;
    }
    const response = await fetchJson(
      `${apiUrl}/chat/id?userId=${userId}&tenantId=${tenantId}`
    );

    return response;
  } catch (error) {
    console.error("Error in getChat service:", error);
    throw error;
  }
};

export const deleteChat = async (id) => {
  try {
    const url = `${apiUrl}/chat/${id}?id=${id}`;
    const response = await fetch(url, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Network response was not ok");
    }
    return await response.json();
  } catch (error) {
    console.log({ "delete Chat api error": error });
    return { error: error.message || "Error deleting Chat" };
  }
};
