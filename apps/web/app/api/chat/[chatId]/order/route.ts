import { NextResponse } from "next/server";
import db from "@/lib/shared-db";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/next-auth";

export async function PUT(
  req: Request,
  { params }: { params: { chatId: string } }
) {
  try {
    const session: any = await getServerSession(authOptions);
    if (!session) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const { order, groupId } = await req.json();
    
    if (typeof order !== "number") {
      return new NextResponse("Invalid order value", { status: 400 });
    }

    // First, verify the chat belongs to the user
    const existingChat = await db.chat.findFirst({
      where: {
        id: params.chatId,
        userId: session?.userId,
      },
    });

    if (!existingChat) {
      return new NextResponse("Chat not found", { status: 404 });
    }

    // Get all chats in the same container (group or ungrouped)
    const chatsInContainer = await db.chat.findMany({
      where: {
        userId: session?.userId,
        tenantId: existingChat.tenantId,
        groupId: groupId || null,
      },
      orderBy: {
        createdAt: "desc", // Default ordering
      },
    });

    // Create a transaction to update the order
    await db.$transaction(async (tx) => {
      // Update all chats to have proper order values
      for (let i = 0; i < chatsInContainer.length; i++) {
        const chat = chatsInContainer[i];
        let newOrder = i;
        
        // If this is the chat being moved, set it to the target order
        if (chat.id === params.chatId) {
          newOrder = order;
        }
        // If this chat's current position is at or after the target order,
        // and it's not the chat being moved, shift it
        else if (i >= order) {
          newOrder = i + 1;
        }

        await tx.chat.update({
          where: { id: chat.id },
          data: { 
            // Note: We're using createdAt as a proxy for order since there's no order field
            // In a real implementation, you'd add an 'order' field to the Chat model
            updatedAt: new Date(Date.now() + newOrder * 1000) // Hack to maintain order
          },
        });
      }
    });

    // Return the updated chat
    const updatedChat = await db.chat.findUnique({
      where: { id: params.chatId },
    });

    return NextResponse.json(updatedChat);
  } catch (error) {
    console.error("[CHAT_ORDER_PUT]", error);
    return NextResponse.json({ error: "Internal Error" }, { status: 500 });
  }
}
